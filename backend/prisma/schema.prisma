generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                 Int            @id @default(autoincrement())
  name               String
  email              String         @unique
  phone              String?        @unique
  password_hash      String
  role               String // "student", "teacher", "parent", "moderator", "admin"
  language           String?
  status             String // "pending", "active", "deleted"
  created_at         DateTime       @default(now())
  updated_at         DateTime       @updatedAt
  teacher            Teacher?
  student            Student?
  parent             Parent?
  payments           Payment[]
  reports            Report[]
  flags              Flag[]
  otp                Otp[]
  data_requests      DataRequest[]
  notifications      Notification[] @relation("Recipient")
  audit_logs         AuditLog[]     @relation("Actor")
  organization_admin Organization?  @relation("OrganizationAdmin")
}

model Teacher {
  id                Int               @id @default(autoincrement())
  user_id           Int               @unique
  user              User              @relation(fields: [user_id], references: [id])
  subjects          String[]
  rates             Float
  availability      Json
  verified_status   String // "pending", "approved", "rejected"
  intro_video_url   String?
  certificates      String[] // URLs or file paths
  organization_id   Int?
  organization      Organization?     @relation(fields: [organization_id], references: [id], onDelete: Cascade)
  lessons           Lesson[]          @relation("TeacherLessons")
  approval_requests ApprovalRequest[]
}

model Student {
  id              Int           @id @default(autoincrement())
  user_id         Int           @unique
  user            User          @relation(fields: [user_id], references: [id])
  grade_level     String
  subscription_id Int?          @unique
  subscription    Subscription? @relation
  created_at      DateTime      @default(now())
  parents         Parent[]      @relation("ParentStudent")
  lessons         Lesson[]      @relation("StudentLessons")
}

model Parent {
  id           Int          @id @default(autoincrement())
  user_id      Int          @unique
  user         User         @relation(fields: [user_id], references: [id])
  children     Student[]    @relation("ParentStudent")
  e_signatures ESignature[]
}

model Lesson {
  id             Int      @id @default(autoincrement())
  teacher_id     Int
  student_id     Int
  teacher        Teacher  @relation("TeacherLessons", fields: [teacher_id], references: [id])
  student        Student  @relation("StudentLessons", fields: [student_id], references: [id])
  scheduled_time DateTime
  status         String // "scheduled", "completed", "canceled"
  recording_url  String?
  feedback       String?
  quiz_url       String?
  created_at     DateTime @default(now())
}

model Organization {
  id            Int       @id @default(autoincrement())
  name          String
  admin_user_id Int       @unique
  admin         User      @relation("OrganizationAdmin", fields: [admin_user_id], references: [id])
  created_at    DateTime  @default(now())
  teachers      Teacher[]
}

model Subscription {
  id           Int          @id @default(autoincrement())
  student_id   Int          @unique
  student      Student      @relation(fields: [student_id], references: [id], onDelete: Cascade)
  plan_type    String // "single_lesson", "learning_plan"
  num_sessions Int
  price        Float
  status       String // "active", "canceled"
  created_at   DateTime     @default(now())
  updated_at   DateTime     @updatedAt
  e_signatures ESignature[]
}

model Payment {
  id          Int      @id @default(autoincrement())
  user_id     Int
  user        User     @relation(fields: [user_id], references: [id])
  amount      Float
  currency    String
  method      String
  status      String
  receipt_url String?
  created_at  DateTime @default(now())
}

model Report {
  id          Int      @id @default(autoincrement())
  user_id     Int
  user        User     @relation(fields: [user_id], references: [id])
  type        String
  description String
  status      String
  created_at  DateTime @default(now())
}

model Flag {
  id          Int      @id @default(autoincrement())
  user_id     Int
  user        User     @relation(fields: [user_id], references: [id])
  type        String
  description String
  status      String
  created_at  DateTime @default(now())
}

model Otp {
  id         Int      @id @default(autoincrement())
  user_id    Int
  user       User     @relation(fields: [user_id], references: [id])
  code       String
  expires_at DateTime
  created_at DateTime @default(now())
}

model ApprovalRequest {
  id         Int      @id @default(autoincrement())
  teacher_id Int
  teacher    Teacher  @relation(fields: [teacher_id], references: [id])
  status     String
  comments   String?
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
}

model AuditLog {
  id         Int      @id @default(autoincrement())
  actor_id   Int
  actor      User     @relation("Actor", fields: [actor_id], references: [id])
  action     String
  details    Json
  created_at DateTime @default(now())
}

model DataRequest {
  id         Int      @id @default(autoincrement())
  user_id    Int
  user       User     @relation(fields: [user_id], references: [id])
  type       String
  status     String
  created_at DateTime @default(now())
}

model Notification {
  id           Int      @id @default(autoincrement())
  recipient_id Int
  recipient    User     @relation("Recipient", fields: [recipient_id], references: [id])
  type         String
  message      String
  status       String
  created_at   DateTime @default(now())
}

model ESignature {
  id              Int          @id @default(autoincrement())
  parent_id       Int
  parent          Parent       @relation(fields: [parent_id], references: [id])
  subscription_id Int
  subscription    Subscription @relation(fields: [subscription_id], references: [id])
  signature       String
  created_at      DateTime     @default(now())
}
