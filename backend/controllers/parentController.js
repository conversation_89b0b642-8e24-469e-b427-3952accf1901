const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Links a student to the authenticated parent's profile.
// - Input: JSON body with student_id; JWT in the Authorization header.
// - Output: JSON response with a success message, or an error message if the student is already linked or not found.
const linkChild = async (req, res) => {
  const { student_id } = req.body;
  try {
    if (req.user.role !== 'parent') return res.status(403).json({ error: 'Unauthorized' });
    const student = await prisma.student.findUnique({ where: { id: student_id } });
    if (!student) return res.status(404).json({ error: 'Student not found' });
    if (student.parent_id) return res.status(400).json({ error: 'Student already linked to another parent' });
    await prisma.student.update({
      where: { id: student_id },
      data: { parents: { connect: { user_id: req.user.id } } },
    });
    res.json({ message: 'Child linked successfully' });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Creates an e-signature for a Learning Plan subscription.
// - Input: JSON body with subscription_id, signature (string); JWT in the Authorization header.
// - Output: JSON response with a success message and signature ID, or an error message if the request fails.
const signLearningPlan = async (req, res) => {
  const { subscription_id, signature } = req.body;
  try {
    if (req.user.role !== 'parent') return res.status(403).json({ error: 'Unauthorized' });
    const eSignature = await prisma.eSignature.create({
      data: {
        parent_id: req.user.id,
        subscription_id,
        signature,
      },
    });
    await prisma.notification.create({
      data: {
        recipient_id: req.user.id,
        type: 'learning_plan_signed',
        message: `Learning Plan ${subscription_id} signed`,
        status: 'sent',
      },
    });
    res.status(201).json({ message: 'Learning Plan signed', signatureId: eSignature.id });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Schedules a parent-teacher meeting by creating a lesson with a specific type.
// - Input: JSON body with teacher_id, scheduled_time; JWT in the Authorization header.
// - Output: JSON response with a success message and lesson ID, or an error message if scheduling fails.
const scheduleMeeting = async (req, res) => {
  const { teacher_id, scheduled_time } = req.body;
  try {
    if (req.user.role !== 'parent') return res.status(403).json({ error: 'Unauthorized' });
    const lesson = await prisma.lesson.create({
      data: {
        teacher_id,
        student_id: 0, // Placeholder, as this is a meeting
        scheduled_time: new Date(scheduled_time),
        status: 'scheduled',
        feedback: 'Parent-Teacher Meeting',
      },
    });
    await prisma.notification.createMany({
      data: [
        { recipient_id: req.user.id, type: 'meeting_scheduled', message: `Meeting scheduled with teacher ${teacher_id}`, status: 'sent' },
        { recipient_id: teacher_id, type: 'meeting_scheduled', message: `Meeting scheduled with parent ${req.user.id}`, status: 'sent' },
      ],
    });
    res.status(201).json({ message: 'Meeting scheduled', lessonId: lesson.id });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

module.exports = { linkChild, signLearningPlan, scheduleMeeting };