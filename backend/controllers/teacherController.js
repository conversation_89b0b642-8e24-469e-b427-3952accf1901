const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Updates the authenticated teacher's profile with subjects, pricing, availability, and certificates.
// - Input: JSON body with subjects (array), rates (float), availability (JSON), certificates (array); JWT in the Authorization header.
// - Output: JSON response with the updated teacher profile, or an error message if the update fails.
const updateTeacherProfile = async (req, res) => {
  const { subjects, rates, availability, certificates, intro_video_url } = req.body;
  try {
    if (req.user.role !== 'teacher') return res.status(403).json({ error: 'Unauthorized' });
    const teacher = await prisma.teacher.update({
      where: { user_id: req.user.id },
      data: { subjects, rates, availability, certificates, intro_video_url },
    });
    res.json(teacher);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Submits an approval request for a teacher's profile to be reviewed by a moderator.
// - Input: JWT in the Authorization header.
// - Output: JSON response with a success message and request ID, or an error message if the request fails.
const submitApprovalRequest = async (req, res) => {
  try {
    if (req.user.role !== 'teacher') return res.status(403).json({ error: 'Unauthorized' });
    const request = await prisma.approvalRequest.create({
      data: {
        teacher_id: req.user.id,
        status: 'pending',
      },
    });
    // Notify moderators (simplified, assumes moderator user IDs are known)
    await prisma.notification.create({
      data: {
        recipient_id: 1, // Replace with actual moderator ID
        type: 'teacher_approval',
        message: `New teacher approval request from user ${req.user.id}`,
        status: 'sent',
      },
    });
    res.status(201).json({ message: 'Approval request submitted', requestId: request.id });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Adds feedback and/or a quiz URL to a lesson after completion.
// - Input: JSON body with lesson_id, feedback (string, optional), quiz_url (string, optional); JWT in the Authorization header.
// - Output: JSON response with the updated lesson, or an error message if the update fails.
const addLessonFeedback = async (req, res) => {
  const { lesson_id, feedback, quiz_url } = req.body;
  try {
    if (req.user.role !== 'teacher') return res.status(403).json({ error: 'Unauthorized' });
    const lesson = await prisma.lesson.update({
      where: { id: lesson_id, teacher_id: req.user.id },
      data: { feedback, quiz_url },
    });
    res.json(lesson);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

module.exports = { updateTeacherProfile, submitApprovalRequest, addLessonFeedback };