const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

dotenv.config();
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api/users', require('./routes/userRoutes'));
app.use('/api/teachers', require('./routes/teacherRoutes'));
app.use('/api/lessons', require('./routes/lessonRoutes'));
app.use('/api/payments', require('./routes/paymentRoutes'));
app.use('/api/reports', require('./routes/reportRoutes'));
app.use('/api/security', require('./routes/securityRoutes'));
app.use('api/flag' , require('./routes/flagRoutes'));
app.use('api/admin' , require('./routes/adminRoutes'));
app.use('api/moderator' , require('./routes/moderatorRoutes'));
app.use('api/parent' , require('./routes/parentRoutes'));

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));