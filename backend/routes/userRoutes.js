const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const auth = require('../middleware/auth');

// Register a new user
router.post('/register', userController.register);

// Confirm OTP for user registration
router.post('/confirm-otp', userController.confirmOtp);

// Login user and return JWT
router.post('/login', userController.login);

// Get authenticated user's profile
router.get('/profile', auth, userController.getProfile);

// Update authenticated user's profile
router.put('/profile', auth, userController.updateProfile);

// Delete a user (soft delete)
router.delete('/:id', auth, userController.deleteUser);

// Request GDPR data export or deletion
router.post('/data-request', auth, userController.requestData);

module.exports = router;