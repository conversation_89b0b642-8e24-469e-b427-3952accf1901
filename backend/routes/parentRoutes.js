const express = require("express");
const router = express.Router();
const parentController = require("../controllers/parentController");
const auth = require("../middleware/auth");

// Link a student to the parent's profile
router.post("/link-child", auth(), parentController.linkChild);

// Create an e-signature for a Learning Plan
router.post("/sign-plan", auth(), parentController.signLearningPlan);

// Schedule a parent-teacher meeting
router.post("/schedule-meeting", auth(), parentController.scheduleMeeting);

module.exports = router;
