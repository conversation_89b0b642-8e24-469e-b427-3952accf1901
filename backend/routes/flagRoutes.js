const express = require("express");
const router = express.Router();
const flagController = require("../controllers/flagController");
const auth = require("../middleware/auth");

// Flag a user for an issue
router.post("/flag", auth(), flagController.flagUser);

// Retrieve flagged incidents (for moderators/admins)
router.get("/incidents", auth(), flagController.getIncidents);

module.exports = router;
